function create_analysis_plots(actuals, predictions, data, r_squared, rmse)
%% 创建分析图表
% 输入:
%   actuals - 实际值向量
%   predictions - 预测值向量
%   data - 实验数据表
%   r_squared - 决定系数
%   rmse - 均方根误差

% 设置中文字体
set(0, 'DefaultAxesFontName', 'SimHei');
set(0, 'DefaultTextFontName', 'SimHei');

% 创建图表
figure('Position', [100, 100, 1200, 800]);

% 1. 预测值vs实际值散点图
subplot(2, 3, 1);
scatter(actuals, predictions, 60, 'filled', 'MarkerFaceAlpha', 0.7);
hold on;

% 添加完美预测线
min_val = min([min(actuals), min(predictions)]);
max_val = max([max(actuals), max(predictions)]);
plot([min_val, max_val], [min_val, max_val], 'r--', 'LineWidth', 2);

xlabel('实际值 (%)');
ylabel('预测值 (%)');
title(sprintf('预测值 vs 实际值\nR² = %.4f, RMSE = %.4f', r_squared, rmse));
grid on;
axis equal;
xlim([min_val, max_val]);
ylim([min_val, max_val]);

% 2. 残差图
subplot(2, 3, 2);
residuals = predictions - actuals;
scatter(actuals, residuals, 60, 'filled', 'MarkerFaceAlpha', 0.7);
hold on;
yline(0, 'r--', 'LineWidth', 2);

xlabel('实际值 (%)');
ylabel('残差 (预测值 - 实际值)');
title('残差分析');
grid on;

% 3. 残差直方图
subplot(2, 3, 3);
histogram(residuals, 10, 'FaceAlpha', 0.7, 'EdgeColor', 'black');
xlabel('残差');
ylabel('频数');
title('残差分布');
grid on;

% 4. 温度效应分析
subplot(2, 3, 4);
temp_unique = unique(data{:, 2});  % 温度列
temp_actual_mean = zeros(size(temp_unique));
temp_pred_mean = zeros(size(temp_unique));

for i = 1:length(temp_unique)
    idx = data{:, 2} == temp_unique(i);
    temp_actual_mean(i) = mean(actuals(idx));
    temp_pred_mean(i) = mean(predictions(idx));
end

x = 1:length(temp_unique);
width = 0.35;
bar(x - width/2, temp_actual_mean, width, 'DisplayName', '实际值', 'FaceAlpha', 0.7);
hold on;
bar(x + width/2, temp_pred_mean, width, 'DisplayName', '预测值', 'FaceAlpha', 0.7);

xlabel('温度 (°C)');
ylabel('孔面积占比 (%)');
title('温度效应对比');
xticks(x);
xticklabels(arrayfun(@num2str, temp_unique, 'UniformOutput', false));
legend;
grid on;

% 5. 湿度效应分析
subplot(2, 3, 5);
humidity_unique = unique(data{:, 3});  % 湿度列
humidity_actual_mean = zeros(size(humidity_unique));
humidity_pred_mean = zeros(size(humidity_unique));

for i = 1:length(humidity_unique)
    idx = data{:, 3} == humidity_unique(i);
    humidity_actual_mean(i) = mean(actuals(idx));
    humidity_pred_mean(i) = mean(predictions(idx));
end

x_h = 1:length(humidity_unique);
bar(x_h - width/2, humidity_actual_mean, width, 'DisplayName', '实际值', 'FaceAlpha', 0.7);
hold on;
bar(x_h + width/2, humidity_pred_mean, width, 'DisplayName', '预测值', 'FaceAlpha', 0.7);

xlabel('湿度 (%)');
ylabel('孔面积占比 (%)');
title('湿度效应对比');
xticks(x_h);
xticklabels(arrayfun(@num2str, humidity_unique, 'UniformOutput', false));
legend;
grid on;

% 6. 固含量效应分析
subplot(2, 3, 6);
solid_unique = unique(data{:, 4});  % 固含量列
solid_actual_mean = zeros(size(solid_unique));
solid_pred_mean = zeros(size(solid_unique));

for i = 1:length(solid_unique)
    idx = data{:, 4} == solid_unique(i);
    solid_actual_mean(i) = mean(actuals(idx));
    solid_pred_mean(i) = mean(predictions(idx));
end

x_s = 1:length(solid_unique);
bar(x_s - width/2, solid_actual_mean, width, 'DisplayName', '实际值', 'FaceAlpha', 0.7);
hold on;
bar(x_s + width/2, solid_pred_mean, width, 'DisplayName', '预测值', 'FaceAlpha', 0.7);

xlabel('固含量 (%)');
ylabel('孔面积占比 (%)');
title('固含量效应对比');
xticks(x_s);
xticklabels(arrayfun(@num2str, solid_unique, 'UniformOutput', false));
legend;
grid on;

% 调整布局并保存
sgtitle('参数拟合结果分析', 'FontSize', 16, 'FontWeight', 'bold');
saveas(gcf, '参数拟合结果分析.png');
saveas(gcf, '参数拟合结果分析.fig');

fprintf('   图表已保存: 参数拟合结果分析.png\n');

end
