%% 问题二：多孔膜孔面积占比机理模型参数拟合
% 基于问题一建立的机理模型，拟合10个待定参数
% 严格遵循User Guidelines - 整理版.md

clear; clc; close all;

fprintf('=== 问题二：参数拟合程序 ===\n\n');

%% 第一步：加载实验数据
fprintf('1. 加载实验数据...\n');
try
    data = readtable('../附件2_Sheet1.csv', 'Encoding', 'UTF-8', 'VariableNamingRule', 'preserve');
    fprintf('   ✅ 成功加载 %d 组实验数据\n', height(data));

    % 获取列名
    col_names = data.Properties.VariableNames;
    fprintf('   列名: %s\n', strjoin(col_names, ', '));

    % 显示数据范围
    fprintf('   数据范围：\n');
    fprintf('   - 温度: %.0f-%.0f°C\n', min(data{:,2}), max(data{:,2}));
    fprintf('   - 湿度: %.0f-%.0f%%\n', min(data{:,3}), max(data{:,3}));
    fprintf('   - 固含量: %.0f-%.0f%%\n', min(data{:,4}), max(data{:,4}));
    fprintf('   - 孔面积占比: %.2f-%.2f%%\n', min(data{:,5}), max(data{:,5}));

catch ME
    fprintf('   ❌ 数据加载失败: %s\n', ME.message);
    return;
end

%% 第二步：定义已知常量
fprintf('\n2. 定义已知常量...\n');

% 通用物理常量
constants.k_B = 1.380649e-23;  % 玻尔兹曼常数 (J/K)
constants.pi = pi;             % 圆周率

% 材料物性参数 (kg/m³)
constants.rho_D = 944;   % DMF密度
constants.rho_S = 1261;  % 环丁砜密度
constants.rho_C = 1300;  % 醋酸纤维素密度

% Antoine方程常数 (DMF)
constants.A = 6.09451;
constants.B = 2725.96;
constants.C = 28.209;

% 实验初始条件
constants.m_D0 = 0.024;  % DMF初始质量 (kg)
constants.m_S0 = 0.006;  % 环丁砜初始质量 (kg)

fprintf('   ✅ 已知常量定义完成\n');

%% 第三步：设置参数优化边界
fprintf('\n3. 设置参数优化边界...\n');

% 参数名称（严格按照User Guidelines符号规范）
param_names = {'k_1', 'k_h', 'S_S', 'S_C', 'k_v', 'r_s', 'k_{form}', 'k_{grow}', 'k_p', '\eta_0'};

% 参数边界 [下界, 上界]
bounds = [
    1e-8,  1e-6;     % k_1: 蒸发速率参数
    0.01,  0.1;      % k_h: 湿度抑制系数
    0.5,   2.0;      % S_S: 环丁砜溶解度
    0.1,   1.0;      % S_C: 醋酸纤维素溶解度
    1e3,   1e5;      % k_v: 速度比例系数
    1e-8,  1e-7;     % r_s: 小液滴半径
    1e-3,  1e-1;     % k_form: 形成效率系数
    1e-15, 1e-12;    % k_grow: 吸收效率系数
    1e3,   1e5;      % k_p: 孔面积比例系数
    0.002, 0.005     % eta_0: 初始溶液粘度
];

fprintf('   ✅ 参数边界设置完成，共 %d 个待拟合参数\n', size(bounds, 1));

%% 第四步：定义目标函数
fprintf('\n4. 定义目标函数...\n');

% 目标函数：最小化预测值与实际值的平方误差和
objective_function = @(params) calculate_total_error(params, data, constants);

fprintf('   ✅ 目标函数定义完成\n');

%% 第五步：执行参数优化
fprintf('\n5. 开始参数优化...\n');
fprintf('   使用遗传算法进行全局优化...\n');

% 设置遗传算法选项
options = optimoptions('ga', ...
    'Display', 'iter', ...
    'MaxGenerations', 100, ...
    'PopulationSize', 50, ...
    'FunctionTolerance', 1e-6, ...
    'UseParallel', false);

% 执行优化
tic;
[optimal_params, fval, exitflag, output] = ga(objective_function, 10, [], [], [], [], ...
    bounds(:,1), bounds(:,2), [], options);
optimization_time = toc;

%% 第六步：输出优化结果
fprintf('\n6. 优化结果分析:\n');

if exitflag > 0
    fprintf('   ✅ 优化成功完成！\n');
    fprintf('   优化时间: %.2f 秒\n', optimization_time);
    fprintf('   最终误差: %.6f\n', fval);
    fprintf('   迭代次数: %d\n', output.generations);
    
    % 输出最优参数
    fprintf('\n   最优参数结果:\n');
    for i = 1:length(optimal_params)
        fprintf('   %s = %.6e\n', param_names{i}, optimal_params(i));
    end
    
else
    fprintf('   ❌ 优化失败，退出标志: %d\n', exitflag);
    return;
end

%% 第七步：模型验证和结果分析
fprintf('\n7. 模型验证和结果分析...\n');

% 计算所有预测值
predictions = zeros(height(data), 1);
for i = 1:height(data)
    T = data{i, 2};        % 温度
    H = data{i, 3};        % 湿度
    SC = data{i, 4};       % 固含量

    predictions(i) = simulate_single_experiment(optimal_params, T, H, SC, constants);
end

% 计算统计指标
actuals = data{:, 5};  % 孔面积占比
r_squared = calculate_r_squared(actuals, predictions);
rmse = sqrt(mean((predictions - actuals).^2));
mae = mean(abs(predictions - actuals));
mape = mean(abs((predictions - actuals) ./ actuals)) * 100;

fprintf('   统计指标:\n');
fprintf('   - R² 决定系数: %.4f\n', r_squared);
fprintf('   - RMSE 均方根误差: %.4f\n', rmse);
fprintf('   - MAE 平均绝对误差: %.4f\n', mae);
fprintf('   - MAPE 平均绝对百分比误差: %.2f%%\n', mape);

%% 第八步：保存结果和生成图表
fprintf('\n8. 保存结果和生成图表...\n');

% 保存结果到表格
results_table = data;
results_table.("预测值") = predictions;
results_table.("绝对误差") = abs(predictions - actuals);
results_table.("相对误差(%)") = abs((predictions - actuals) ./ actuals) * 100;

writetable(results_table, '拟合结果对比.csv', 'Encoding', 'UTF-8');
fprintf('   ✅ 结果已保存到: 拟合结果对比.csv\n');

% 生成分析图表
create_analysis_plots(actuals, predictions, data, r_squared, rmse);
fprintf('   ✅ 分析图表已生成\n');

%% 第九步：参数敏感性分析
fprintf('\n9. 参数敏感性分析...\n');
sensitivity_analysis(optimal_params, data, constants, param_names);

%% 第十步：生成总结报告
fprintf('\n10. 生成总结报告...\n');
generate_summary_report(optimal_params, param_names, r_squared, rmse, mae, mape, optimization_time);

fprintf('\n=== 问题二参数拟合完成 ===\n');
fprintf('✅ 拟合精度: R² = %.4f\n', r_squared);
fprintf('✅ 总耗时: %.2f 秒\n', optimization_time);

%% 保存工作空间
save('parameter_fitting_results.mat', 'optimal_params', 'param_names', 'constants', ...
     'r_squared', 'rmse', 'mae', 'mape', 'results_table');
fprintf('✅ 工作空间已保存到: parameter_fitting_results.mat\n');
