function P_p_predicted = simulate_single_experiment(params, T, H, SC, constants)
%% 单次实验仿真
% 基于问题一的机理模型，预测给定条件下的孔面积占比
% 输入:
%   params - 参数向量 [k_1, k_h, S_S, S_C, k_v, r_s, k_form, k_grow, k_p, eta_0]
%   T - 温度 (°C)
%   H - 湿度 (%)
%   SC - 固含量 (%)
%   constants - 已知常量结构体
% 输出:
%   P_p_predicted - 预测的孔面积占比 (%)

try
    % 解包参数
    k_1 = params(1);
    k_h = params(2);
    S_S = params(3);
    S_C = params(4);
    k_v = params(5);
    r_s = params(6);
    k_form = params(7);
    k_grow = params(8);
    k_p = params(9);
    eta_0 = params(10);
    
    % 参数合理性检查
    if any(params <= 0) || r_s > 1e-6 || eta_0 > 0.1
        P_p_predicted = -1;
        return;
    end
    
    % 计算初始质量
    m_D0 = constants.m_D0;
    m_S0 = constants.m_S0;
    m_C0 = (SC / 100) * (m_D0 + m_S0);
    
    % 初始状态设置
    % 假设初始时部分环丁砜已析出，50%为小液滴
    m_S1_initial = max(0, m_S0 - m_D0 * S_S);
    m_S2_initial = 0.5 * m_S1_initial;  % 小液滴质量
    m_S3_initial = 0.5 * m_S1_initial;  % 大液滴质量
    
    % 设置ODE初值
    y0 = [m_D0; m_S2_initial; m_S3_initial];
    
    % 时间范围：足够长以确保DMF基本蒸发完
    t_span = [0, 3600];  % 1小时
    
    % ODE求解选项
    options = odeset('RelTol', 1e-6, 'AbsTol', 1e-8, 'NonNegative', [1,2,3]);
    
    % 求解ODE系统
    [t, y] = ode45(@(t, y) ode_system(t, y, params, T, H, m_C0, constants), ...
                   t_span, y0, options);
    
    % 检查求解是否成功
    if isempty(y) || any(isnan(y(end,:))) || any(isinf(y(end,:)))
        P_p_predicted = -1;
        return;
    end
    
    % 提取最终大液滴质量
    m_S3_final = y(end, 3);
    
    % 计算孔面积占比
    P_p_predicted = k_p * m_S3_final;
    
    % 确保结果为正数且合理
    if P_p_predicted < 0 || P_p_predicted > 100
        P_p_predicted = -1;
    end
    
catch ME
    % 如果计算出错，返回错误标志
    P_p_predicted = -1;
end

end

function dydt = ode_system(t, y, params, T, H, m_C0, constants)
%% ODE系统函数
% y = [m_D, m_S2, m_S3] (DMF质量, 小液滴质量, 大液滴质量)

% 解包状态变量
m_D = max(y(1), 1e-6);
m_S2 = max(y(2), 0);
m_S3 = max(y(3), 0);

% 解包参数
k_1 = params(1);
k_h = params(2);
S_S = params(3);
S_C = params(4);
k_v = params(5);
r_s = params(6);
k_form = params(7);
k_grow = params(8);
k_p = params(9);
eta_0 = params(10);

% 计算中间变量
% 1. 溶液体积
V_sol = m_D/constants.rho_D + constants.m_S0/constants.rho_S + m_C0/constants.rho_C;
V_sol = max(V_sol, 1e-6);

% 2. DMF浓度
c = m_D / V_sol;

% 3. 饱和蒸气压 (Antoine方程)
T_K = T + 273.15;
log_P_bar = constants.A - constants.B / (T_K + constants.C);
P_sat = (10^log_P_bar) * 1e5;  % 转换为Pa

% 4. 析出判据
m_S1 = max(0, constants.m_S0 - m_D * S_S);
m_C1 = max(0, m_C0 - m_D * S_C);

% 5. 粘度计算
phi_p = m_C1 / constants.rho_C / V_sol;
eta = eta_0 * (1 + 2.5 * phi_p);
eta = max(eta, eta_0);

% 6. 扩散系数和液滴速度
D = constants.k_B * T_K / (6 * constants.pi * eta * r_s);
v_bar = k_v * sqrt(D);

% 7. DMF蒸发速率
dm_D_dt = -k_1 * c * P_sat * (1 - k_h * H / 100);
dm_D_dt = min(dm_D_dt, 0);  % 确保只能蒸发

% 8. 析出速率
if m_D > 1e-6
    dm_S1_dt = -S_S * dm_D_dt;
else
    dm_S1_dt = 0;
end

% 9. 液滴动力学
if m_S2 > 1e-6 && r_s > 1e-9
    % 小液滴数量密度
    m_drop = (4/3) * constants.pi * r_s^3 * constants.rho_S;
    n = m_S2 / (m_drop * V_sol);
    
    % 碰撞频率
    Z_bar = sqrt(2) * n * constants.pi * r_s^2 * v_bar;
    
    % 形成和吸收速率
    dm_form_dt = k_form * Z_bar;
    dm_grow_dt = k_grow * v_bar * (m_S2/V_sol) * m_S3;
else
    dm_form_dt = 0;
    dm_grow_dt = 0;
end

% 10. 质量平衡
dm_S2_dt = dm_S1_dt - dm_form_dt - dm_grow_dt;
dm_S3_dt = dm_form_dt + dm_grow_dt;

% 返回导数向量
dydt = [dm_D_dt; dm_S2_dt; dm_S3_dt];

end
