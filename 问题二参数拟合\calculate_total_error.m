function total_error = calculate_total_error(params, data, constants)
%% 计算总平方误差
% 输入:
%   params - 待拟合参数向量 [k_1, k_h, S_S, S_C, k_v, r_s, k_form, k_grow, k_p, eta_0]
%   data - 实验数据表
%   constants - 已知常量结构体
% 输出:
%   total_error - 总平方误差

total_error = 0;

% 遍历所有实验数据点
for i = 1:height(data)
    try
        % 提取实验条件 (使用数字索引访问列)
        T = data{i, 2};        % 温度
        H = data{i, 3};        % 湿度
        SC = data{i, 4};       % 固含量
        actual_value = data{i, 5}; % 孔面积占比
        
        % 计算预测值
        predicted_value = simulate_single_experiment(params, T, H, SC, constants);
        
        % 检查预测值的合理性
        if isnan(predicted_value) || isinf(predicted_value) || predicted_value < 0
            total_error = total_error + 1e6;  % 惩罚不合理的预测值
        else
            % 计算平方误差
            error = (predicted_value - actual_value)^2;
            total_error = total_error + error;
        end
        
    catch ME
        % 如果计算出错，添加大的惩罚项
        total_error = total_error + 1e6;
    end
end

% 防止数值溢出
if isnan(total_error) || isinf(total_error)
    total_error = 1e6;
end

end
