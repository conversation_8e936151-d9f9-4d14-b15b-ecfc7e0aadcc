function r_squared = calculate_r_squared(actual, predicted)
%% 计算决定系数 R²
% 输入:
%   actual - 实际值向量
%   predicted - 预测值向量
% 输出:
%   r_squared - 决定系数 R²

% 计算总平方和 (TSS)
mean_actual = mean(actual);
TSS = sum((actual - mean_actual).^2);

% 计算残差平方和 (RSS)
RSS = sum((actual - predicted).^2);

% 计算 R²
if TSS == 0
    r_squared = 1;  % 完美拟合情况
else
    r_squared = 1 - RSS / TSS;
end

% 确保 R² 在合理范围内
r_squared = max(r_squared, -1);  % R² 可以为负数

end
