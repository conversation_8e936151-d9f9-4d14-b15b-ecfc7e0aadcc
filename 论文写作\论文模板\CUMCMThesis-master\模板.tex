%\documentclass{cumcmthesis}
\documentclass[withoutpreface,bwprint]{cumcmthesis} %去掉封面与编号页
%添加/取消注释：CTRL+T/U
\title{论文题目}
\tihao{A}            % 题号
\baominghao{4321}    % 报名号
\schoolname{你的大学}
\membera{成员A}
\memberb{成员B}
\memberc{成员C}
\supervisor{指导老师}
\yearinput{2017}     % 年
\monthinput{08}      % 月
\dayinput{22}        % 日

\begin{document}
	\maketitle
	\begin{abstract}
		山区耕地资源匮乏，因此为了提高乡村的经济效益，必须充分利用有限的耕地的资源，而选择合适的农作物品种，指定最优种植策略是其关键之处，也是本文亟待解决的问题。
		
		问题一：本文以某年某耕地所种某农作物的面积作为决策变量，建立以2024年到2030年利润总和为目标函数的优化模型。该模型满足约束有：耕地面积限制、种植面积不宜太小、种植地不能太分散、轮作制度、豆类种植要求、各类型耕地种植农作物类型限制、大白菜、白萝卜和红萝卜的季节性限制和食用菌种植环境要求。为了求解该优化模型，本文选择使用遗传算法求解，最终得到情况1——超过部分滞销，造成浪费的总利润为：29276719.5元；情况2——超过部分按2023年销售价格的50%降价出售的总利润为：42837286.75元
		
		问题二：对于农作物未来预期销售量、亩产量、种植成本和销售价格的不确定性，本文引入满足均分分布的不确定因子，再利用对以上4种指标进行修正；对于农作物潜在的种植风险，综合该乡村地处华北山区，本文考虑了华北地区常出现的两种自然灾害，分别为干旱与寒潮，其主要影响的为农作物的产量。再根据文献得知两种灾害对农作物的产量的影响量大小，将影响量刻画为干旱因子，寒潮因子，分别在两种因子情况下对农作物的亩产量进行修正，最终在问题一模型的基础上建立以2024年到2030年利润总和为目标函数的优化模型。再次利用遗传算法求解得到方案，然后引入了在风险下利润波动指标得到抗风险能力最强的方案，该方案的总利润为：38732308.4505元
		
		问题三：对于农作物之间的替代性，本文对农作物划分了3类替代品集合，处于替代品集合中的农作物互为替代品，因此它们被认为一体，随着某农作物价格上升其替代品的销售量将增加；对于农作物之间的互补性，本文对农作物划分了2类互补品集合，处于互补品集合中的农作物互为互补品，因此它们一荣俱荣、一损俱损，随着某农作物价格上升其互补品的销售量将减少；对于预期销售量与销售价格，种植成本的相关性，本文假设前一年销售量作为今年的市场需求量，再利用供需关系，得到预期销售量与销售价格负相关，预期销售量与种植成本负相关。然后通过查阅资料，对这4种关系进行量化，最终在问题二的基础上，建立以2024年到2030年利润总和为目标函数的优化模型，模拟数据得到总利润为37304570.2468元，与问题二相比来说其总利润发生了下降，主要影响是供需关系，替代性导致买方可能选择更低价的农产品。
		
		在文章的最后总结了模型的优缺，并提出了模型的改进意见。
		
		\keywords{关键词1\quad  关键词2\quad   关键词3}
	\end{abstract}
	%\tableofcontents  %目录
	\section{问题的背景与重述}
	\subsection{问题的背景}
	随着乡村振兴战略的实施，国家对提高乡村经济越发重视。农业经济作为乡村经济最基础的部分，提高农业经济对乡村振兴具有深远的影响。
	
	农作物作为人民日常生活必备食物之一，其市场前景广阔。但由于农作物具有季节性，周期性等自然属性，加之农作物品种众多，价格各异，种植成本不同且我国耕地资源匮乏，因此根据乡村的实际情况，选择合适的农作物品种，优化种植策略，充分利用有限的耕地的资源，对于乡村实现更高的经济效益至关重要。
	
	\subsection{问题的重述}
	某山村地处华北山区，其耕地面积1213亩，包含平旱地、梯田、山坡地、水浇地、普通大棚与智慧大棚共6种类型。为了提高农作物的产量，每块耕地3年内至少种植一次豆科植物且每种农作物不能在同一块地连续重茬种植。附件1给出了该乡村耕地资源的详细情况与可种植农作物的品种；附件2给出了该乡村2023年农作物的种植情况与经济效益。
	
	问题一：假定各种农作物未来的预期销售量、种植成本、亩产量和销售价格相对于2023年保持稳定，每季种植的农作物在当季销售。如果某种农作物每季总产量超出当季的销售量，超出的产量将被浪费或者以2023年销售价格50%出售。针对上文这2种超出产量的处理方法，分别给出乡村2024~2030年农作物的最优种植方案。
	
	问题二：根据经验，农作物未来的预期销售量、亩产量、种植成本与销售价格均会发生波动，综合考虑各种农作物的预期销售量、亩产量、种植成本和销售价格的不确定性以及潜在的种植风险，给出该乡村2024~2030年农作物的最优种植方案。
	问题三：在实际情况中，农作物之间存在替代性与互补性，而预期销售量与种植成本、销售价格之间也存在经济学关系，再考虑上这些因素给出该乡村2024~2030年农作物的最优种植策略，并通过模拟数据与问题二的结果进行对比。
	
	\section{问题的分析}
	\textbf{针对问题一：}需要在每年预期销售量、种植成本、亩产量和销售价格固定为2023年的情况下，分别求出超出销售量的部分浪费或者以销售价格50%进行出售2种情况的最优种植方案。为了得到2023年的销售量，假设2023农作物全部正常售出，然后从2023年统计的相关数据中探究农作物种植成本、亩产量和销售价格的影响关系，最后以某年某耕地所种某农作物的面积作为决策变量，建立以2024年到2030年利润总和为目标函数的单变量线性规划模型，满足最大耕地面积限制、作物在单块耕地种植面积不宜太小、每种作物每季的种植地不能太分散以及采取轮作制度以及每块耕地三年内至少种植一次豆类作物、作物习性的约束如平旱地、梯田和山坡地每年适宜单季种植粮食类作物（水稻除外）、水稻只能种在水浇地等约束。为了求解该大规模优化模型，需要采用启发式遗传算法进行求解。
	
	\textbf{针对问题二：}在问题一的基础上，对于不确定性因素，农作物的未来预期销售量、亩产量有一定范围内的变化，且受市场价格影响，农作物的种植成本和销售价格也有变化。我们假设在一定范围内变化的随机变量服从均匀分布，如预期销售量、亩产量等；对于农作物的种植成本、蔬菜类作物的销售价格的增长取定值。对于潜在的种植风险因素，我们考虑华北地区因极端气候影响面临的严重自然灾害干旱和寒潮，引入自然灾害因子：寒潮灾害因子和干旱灾害因子，干旱和寒潮对相应季节不同作物的影响也有不同。
	综上所述，结合不确定性因素和潜在的种植风险，同问题一建立以2024年到2030年利润总和为目标函数的单变量线性规划模型，并采用启发式遗传算法进行求解，再引入利润波动指标来选择抗风险好的方案，得到两种情况的最优种植方案。
	
	\section{模型的假设}
	\begin{enumerate}
		\item 假设每种农作物种植面积不小于种植地的一半。
		\item 假设为了管理方便，粮食类农作物在每块耕地上仅能种植一种。
		\item 假设2023年产销相等。
	\end{enumerate}
	\section{符号说明}
 \begin{table}[!htbp]\centering
 	\begin{tabular}{ccc}
 		\toprule[1.5pt]
 		符号   & 含义   & 单位  \\
 		\midrule[1pt]
 		a & 表中数据(1,2) &  元\\
 		b & 表中数据(2,2) & 亩\\
 		c & 表中数据(m,2) & $ / $ \\
 		\bottomrule[1.5pt]
 	\end{tabular}
 \end{table}
	\section{模型建立与求解}
	\subsection{问题一模型建立与求解}
	
	\subsection{问题二模型建立与求解}
	内容
	\section{灵敏度分析}
	内容
	\section{模型的评价与改进}
	内容
	
%参考文献
\begin{thebibliography}{9}%宽度9
	\bibitem[1]{liuhaiyang2013latex}
	刘海洋.
	\newblock \LaTeX {}入门\allowbreak[J].
	\newblock 电子工业出版社, 北京, 2013.
	\bibitem[2]{mathematical-modeling}
	全国大学生数学建模竞赛论文格式规范 (2020 年 8 月 25 日修改).
	\bibitem{3} \url{https://www.latexstudio.net}
\end{thebibliography}

\newpage
\begin{appendices}
\section{代码}
\begin{lstlisting}[language=matlab]
	kk=2;[mdd,ndd]=size(dd);
	while ~isempty(V)
	[tmpd,j]=min(W(i,V));tmpj=V(j);
	for k=2:ndd
	[tmp1,jj]=min(dd(1,k)+W(dd(2,k),V));
	tmp2=V(jj);tt(k-1,:)=[tmp1,tmp2,jj];
	end
	tmp=[tmpd,tmpj,j;tt];[tmp3,tmp4]=min(tmp(:,1));
	if tmp3==tmpd, ss(1:2,kk)=[i;tmp(tmp4,2)];
	else,tmp5=find(ss(:,tmp4)~=0);tmp6=length(tmp5);
	if dd(2,tmp4)==ss(tmp6,tmp4)
	ss(1:tmp6+1,kk)=[ss(tmp5,tmp4);tmp(tmp4,2)];
	else, ss(1:3,kk)=[i;dd(2,tmp4);tmp(tmp4,2)];
	end;end
	dd=[dd,[tmp3;tmp(tmp4,2)]];V(tmp(tmp4,3))=[];
	[mdd,ndd]=size(dd);kk=kk+1;
	end; S=ss; D=dd(1,:);
\end{lstlisting}
	

\end{appendices}
\end{document}