

请遵循以下步骤完成计算任务。

**步骤一：初始化与数据准备**
1.  在程序中定义所有“已知输入常量”（1.2节）。
2.  加载附件2的实验数据表格。程序需要能够遍历表格的每一行，以获取该组实验的 $T$, $H$, $SC$ 和作为参照标准的“孔面积占比”真实值。

**步骤二：构建机理模型函数 (ODE系统)**
1.  创建一个核心函数，该函数用于描述系统的动态演化，即常微分方程组 (ODEs)。
2.  **输入**：此函数的输入参数应包括：当前时间 $t$、一个包含当前各动态变量值的状态向量 (例如，`y = [m_D, m_S_small, m_S_large]`)、一个包含全部10个“待拟合参数”（1.1节）的向量、以及本次仿真对应的特定实验条件（$T$, $H$, 以及总质量 $m_{S0}$ 和 $m_{C0}$）。
3.  **计算逻辑**：在函数内部，根据机理模型文件，依次计算所有中间变量（如：$V_{sol}$, $c$, $P_{sat}$, $m_{C1}$, $\eta$, $D$, $\overline{v}$ 等）。
4.  **输出**：此函数的返回值应是状态向量中每个变量的变化率（导数），即 `dy/dt = [dm_D/dt, dm_S_small/dt, dm_S_large/dt]`。

**步骤三：封装单次仿真流程**
1.  创建一个封装函数，用于执行一次完整的模拟过程。
2.  **输入**：此函数的输入为一个包含10个待拟合参数的向量和一组实验条件 ($T$, $H$, $SC$)。
3.  **执行流程**：
    * 根据输入的 $SC$ 计算初始质量，设定ODE求解器的初始状态向量 $y_0$。
    * 调用一个标准的ODE数值求解器（如RK45方法），对步骤二中构建的机理模型函数进行积分。积分时间应足够长，以确保DMF完全蒸发、体系达到稳定。
    * 从求解结果中，提取出积分结束时“大液滴的总质量”($m_{S,大液滴,final}$)。
    * 利用参数向量中的 $k_p$，计算出模型预测的孔面积占比：$P_{p,predicted} = k_p \times m_{S,大液滴,final}$。
4.  **输出**：此函数的返回值是单次模拟预测出的孔面积占比 $P_{p,predicted}$。

**步骤四：定义目标函数 (误差函数)**
1.  创建一个目标函数，其目的是评估一套参数的优劣。
2.  **输入**：此函数的唯一输入是一个包含10个待拟合参数的向量。
3.  **计算逻辑**：
    * 初始化总误差为0。
    * 遍历附件2中的全部27组实验数据。
    * 在循环中，对每一组实验条件，调用步骤三的“单次仿真流程”函数，获得该组条件的 $P_{p,predicted}$。
    * 读取该组实验的真实孔面积占比 $P_{p,actual}$，并计算预测值与真实值之间的误差（推荐使用平方误差：$(P_{p,predicted} - P_{p,actual})^2$）。
    * 将该误差累加到总误差上。
4.  **输出**：此函数的返回值是全部27组实验的总误差（Sum of Squared Errors, SSE）。

**步骤五：执行参数优化**
1.  选择一个合适的数值优化算法（建议使用支持边界约束的算法，如L-BFGS-B或差分进化算法）。
2.  为每一个待拟合参数设定一个合理的、有物理意义的取值范围（边界），以提高优化效率和结果的合理性（例如，所有参数都应为正数）。
3.  以步骤四定义的目标函数为优化目标，以待拟合参数为变量，启动优化器。优化器的任务是找到能够使目标函数返回值最小化的那一组参数。

**步骤六：结果验证与输出**
1.  优化过程收敛后，得到一组最优的10个参数值。这是问题二的主要成果。
2.  为检验模型拟合的优劣，请使用这组最优参数，再次执行步骤三的仿真流程，得到全部27组实验的最终预测值。
3.  将这27个预测值与真实值进行对比分析（例如，计算R²决定系数、绘制预测值-真实值散点图），以评估模型的合理性和准确性。