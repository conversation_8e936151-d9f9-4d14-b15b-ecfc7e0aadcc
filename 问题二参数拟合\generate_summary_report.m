function generate_summary_report(optimal_params, param_names, r_squared, rmse, mae, mape, optimization_time)
%% 生成总结报告
% 输入:
%   optimal_params - 最优参数向量
%   param_names - 参数名称
%   r_squared - 决定系数
%   rmse - 均方根误差
%   mae - 平均绝对误差
%   mape - 平均绝对百分比误差
%   optimization_time - 优化时间

% 创建报告内容
report_content = {
    '# 问题二参数拟合结果报告';
    '';
    '## 1. 最优参数结果';
    '';
    '基于问题一建立的机理模型，通过遗传算法优化得到的最优参数如下：';
    '';
    '| 参数 | 符号 | 最优值 | 单位 |';
    '|------|------|--------|------|'
};

% 添加参数表格
units = {'kg/(Pa·s·m³)', '-', '-', '-', 'm^{1/2}·s^{-1/2}', 'm', '-', 's·m^{-3}', '%/kg', 'Pa·s'};
for i = 1:length(optimal_params)
    param_line = sprintf('| %s | %s | %.6e | %s |', ...
        param_names{i}, param_names{i}, optimal_params(i), units{i});
    report_content{end+1} = param_line;
end

% 添加模型性能指标
report_content = [report_content; {
    '';
    '## 2. 模型性能指标';
    '';
    sprintf('- **R² 决定系数**: %.4f', r_squared);
    sprintf('- **RMSE 均方根误差**: %.4f', rmse);
    sprintf('- **MAE 平均绝对误差**: %.4f', mae);
    sprintf('- **MAPE 平均绝对百分比误差**: %.2f%%', mape);
    sprintf('- **优化时间**: %.2f 秒', optimization_time);
    '';
    '## 3. 拟合质量评价';
    ''
}];

% 添加质量评价
if r_squared > 0.9
    quality_assessment = '- **拟合质量**: 优秀 (R² > 0.9)';
    recommendation = '- **建议**: 模型拟合质量很高，可用于工艺优化和预测';
elseif r_squared > 0.8
    quality_assessment = '- **拟合质量**: 良好 (0.8 < R² < 0.9)';
    recommendation = '- **建议**: 模型拟合质量较好，基本可用于工艺指导';
elseif r_squared > 0.7
    quality_assessment = '- **拟合质量**: 一般 (0.7 < R² < 0.8)';
    recommendation = '- **建议**: 模型拟合质量中等，建议进一步优化或增加实验数据';
else
    quality_assessment = '- **拟合质量**: 较差 (R² < 0.7)';
    recommendation = '- **建议**: 模型拟合质量不佳，需要重新考虑模型结构或参数设置';
end

report_content = [report_content; {
    quality_assessment;
    recommendation;
    '';
    '## 4. 模型应用';
    '';
    '### 4.1 预测公式';
    '';
    '基于拟合得到的参数，孔面积占比的预测公式为：';
    '';
    '```';
    'P_p = f(T, H, SC, k)';
    '```';
    '';
    '其中 k 为上述最优参数向量。';
    '';
    '### 4.2 适用范围';
    '';
    '- **温度范围**: 基于实验数据确定';
    '- **湿度范围**: 基于实验数据确定';
    '- **固含量范围**: 基于实验数据确定';
    '';
    '### 4.3 使用说明';
    '';
    '1. 确保输入参数在实验数据范围内';
    '2. 使用 `simulate_single_experiment` 函数进行预测';
    '3. 结果为孔面积占比百分比';
    '';
    '## 5. 文件说明';
    '';
    '本次参数拟合生成的文件包括：';
    '';
    '- `拟合结果对比.csv`: 实验值与预测值对比';
    '- `参数拟合结果分析.png`: 拟合结果可视化分析';
    '- `参数敏感性分析.png`: 参数敏感性分析图';
    '- `parameter_fitting_results.mat`: MATLAB工作空间数据';
    '- `参数拟合报告.md`: 本报告文件';
    '';
    '## 6. 结论';
    '';
    sprintf('通过遗传算法优化，成功拟合了问题一建立的机理模型的10个参数。模型的R²达到%.4f，', r_squared);
    '表明机理模型能够较好地描述多孔膜孔面积占比与工艺条件的关系。';
    '';
    '---';
    '';
    sprintf('*报告生成时间: %s*', datestr(now, 'yyyy-mm-dd HH:MM:SS'));
}];

% 写入报告文件
fid = fopen('参数拟合报告.md', 'w', 'n', 'UTF-8');
if fid == -1
    fprintf('   ❌ 无法创建报告文件\n');
    return;
end

for i = 1:length(report_content)
    fprintf(fid, '%s\n', report_content{i});
end

fclose(fid);
fprintf('   ✅ 总结报告已保存: 参数拟合报告.md\n');

end
