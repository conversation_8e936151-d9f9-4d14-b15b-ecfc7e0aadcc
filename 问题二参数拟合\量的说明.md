好的，遵照您的指示，这是一份纯文字版的、面向编程实现人员的指导文件。

---

### **问题二参数拟合：模型实现与计算说明**

**致代码实现负责人：**

本文档旨在为您提供清晰的、分步骤的编程实现指南，以完成“问题二”中提出的参数拟合任务。本文档不包含具体代码，而是重点阐述计算逻辑、数据流和实现步骤。

#### **第一部分：模型参数与变量总览**

在编程前，请首先明确模型中所有量的角色。我们将它们划分为三类：

**1.1 待拟合参数 (Parameters to be Fitted)**
这是本次任务的核心求解目标。优化算法需要找到这10个参数的最优值。

**注意**: $S_S$, $S_C$, $\eta_0$虽然理论上可通过实验确定，但在实际拟合中作为待拟合参数处理。

1. **$k_1$ (蒸发速率参数)**: 控制DMF蒸发的整体快慢。
2. **$k_h$ (湿度抑制系数)**: 量化湿度对蒸发的阻碍程度。
3. **$S_S$ (环丁砜溶解度)**: 定义DMF对环丁砜的溶解能力。
4. **$S_C$ (醋酸纤维素溶解度)**: 定义DMF对醋酸纤维素的溶解能力。
5. **$k_v$ (速度比例系数)**: 关联微观布朗运动与宏观液滴速度。
6. **$k_{form}$ (形成效率系数)**: 小液滴碰撞聚并为大液滴的效率。
7. **$k_{grow}$ (吸收效率系数)**: 大液滴吸收小液滴的效率。
8. **$k_p$ (孔面积比例系数)**: 将最终大液滴质量转换为孔面积占比的系数。
9. **$r$ (平均小液滴半径)**: 决定液滴运动和碰撞行为的特征尺寸。
10. **$\eta_0$ (初始溶液粘度)**: 这是一个“等效初始粘度”，代表了包含DMF、环丁砜和初始溶解的醋酸纤维素在内的三元混合溶液的整体粘度特性。

**1.2 已知输入常量 (Known Input Constants)**
这些量在所有计算中保持不变，应作为常量在程序中定义。

* **物理与材料常数**:
  * 玻尔兹曼常数 ($k_B$)
  * DMF密度 ($\rho_D$)
  * 环丁砜密度 ($\rho_S$)
  * 醋酸纤维素密度 ($\rho_C$)
    *(注：以上密度值需查阅资料确定)*
* **过程参数**:
  * DMF的Antoine方程常数A, B, C。用于计算饱和蒸气压，注意单位转换。

**1.3 实验条件与初始状态 (Experimental Conditions and Initial State)**
这些量根据不同的实验而变化，是每次仿真计算的输入。

* **控制变量** (从附件2表格中逐行读取)：
  * 蒸发温度 $T$ (°C)
  * 蒸发湿度 $H$ (%)
  * 固含量 $SC$ (%)
* **初始质量** (根据实验条件计算)：
  * DMF初始质量 $m_{D,0}$ 固定为 24g。
  * 环丁砜初始质量 $m_{S,0}$ 固定为 6g。
  * 醋酸纤维素初始质量 $m_{C,0}$ 需要根据该行实验的固含量 $SC$ 计算得出，公式为：$m_{C,0} = (SC / 100) \times (m_{D,0} + m_{S,0})$。

---
