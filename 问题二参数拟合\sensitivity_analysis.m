function sensitivity_analysis(optimal_params, data, constants, param_names)
%% 参数敏感性分析
% 输入:
%   optimal_params - 最优参数向量
%   data - 实验数据
%   constants - 已知常量
%   param_names - 参数名称

fprintf('   执行参数敏感性分析...\n');

% 基准误差
base_error = calculate_total_error(optimal_params, data, constants);

% 扰动幅度
perturbation = 0.1;  % 10%扰动

% 计算每个参数的敏感性
sensitivities = zeros(length(optimal_params), 1);

for i = 1:length(optimal_params)
    % 正向扰动
    params_pos = optimal_params;
    params_pos(i) = params_pos(i) * (1 + perturbation);
    error_pos = calculate_total_error(params_pos, data, constants);
    
    % 负向扰动
    params_neg = optimal_params;
    params_neg(i) = params_neg(i) * (1 - perturbation);
    error_neg = calculate_total_error(params_neg, data, constants);
    
    % 计算敏感性指数
    if base_error > 0
        sensitivity = abs((error_pos + error_neg - 2 * base_error) / (base_error * perturbation^2));
    else
        sensitivity = 0;
    end
    
    sensitivities(i) = sensitivity;
end

% 创建敏感性分析图
figure('Position', [200, 200, 1000, 600]);

% 敏感性柱状图
subplot(1, 2, 1);
bar(sensitivities, 'FaceAlpha', 0.7, 'EdgeColor', 'black');
xlabel('参数');
ylabel('敏感性指数');
title('参数敏感性分析');
xticks(1:length(param_names));
xticklabels(param_names);
xtickangle(45);
grid on;

% 添加数值标签
for i = 1:length(sensitivities)
    text(i, sensitivities(i) + max(sensitivities)*0.02, ...
         sprintf('%.2e', sensitivities(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 8);
end

% 敏感性排序
[sorted_sens, sort_idx] = sort(sensitivities, 'descend');
sorted_names = param_names(sort_idx);

subplot(1, 2, 2);
bar(sorted_sens, 'FaceAlpha', 0.7, 'EdgeColor', 'black');
xlabel('参数（按敏感性排序）');
ylabel('敏感性指数');
title('参数敏感性排序');
xticks(1:length(sorted_names));
xticklabels(sorted_names);
xtickangle(45);
grid on;

% 添加数值标签
for i = 1:length(sorted_sens)
    text(i, sorted_sens(i) + max(sorted_sens)*0.02, ...
         sprintf('%.2e', sorted_sens(i)), ...
         'HorizontalAlignment', 'center', 'FontSize', 8);
end

sgtitle('参数敏感性分析结果', 'FontSize', 14, 'FontWeight', 'bold');
saveas(gcf, '参数敏感性分析.png');
saveas(gcf, '参数敏感性分析.fig');

% 输出敏感性排序结果
fprintf('   参数敏感性排序（从高到低）:\n');
for i = 1:length(sorted_sens)
    fprintf('   %d. %s: %.2e\n', i, sorted_names{i}, sorted_sens(i));
end

fprintf('   敏感性分析图已保存: 参数敏感性分析.png\n');

end
